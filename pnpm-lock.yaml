lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    devDependencies:
      '@tauri-apps/cli':
        specifier: ^1.2.3
        version: 1.6.3

packages:

  '@tauri-apps/cli-darwin-arm64@1.6.3':
    resolution: {integrity: sha512-fQN6IYSL8bG4NvkdKE4sAGF4dF/QqqQq4hOAU+t8ksOzHJr0hUlJYfncFeJYutr/MMkdF7hYKadSb0j5EE9r0A==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [darwin]

  '@tauri-apps/cli-darwin-x64@1.6.3':
    resolution: {integrity: sha512-1yTXZzLajKAYINJOJhZfmMhCzweHSgKQ3bEgJSn6t+1vFkOgY8Yx4oFgWcybrrWI5J1ZLZAl47+LPOY81dLcyA==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [darwin]

  '@tauri-apps/cli-linux-arm-gnueabihf@1.6.3':
    resolution: {integrity: sha512-CjTEr9r9xgjcvos09AQw8QMRPuH152B1jvlZt4PfAsyJNPFigzuwed5/SF7XAd8bFikA7zArP4UT12RdBxrx7w==}
    engines: {node: '>= 10'}
    cpu: [arm]
    os: [linux]

  '@tauri-apps/cli-linux-arm64-gnu@1.6.3':
    resolution: {integrity: sha512-G9EUUS4M8M/Jz1UKZqvJmQQCKOzgTb8/0jZKvfBuGfh5AjFBu8LHvlFpwkKVm1l4951Xg4ulUp6P9Q7WRJ9XSA==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [linux]

  '@tauri-apps/cli-linux-arm64-musl@1.6.3':
    resolution: {integrity: sha512-MuBTHJyNpZRbPVG8IZBN8+Zs7aKqwD22tkWVBcL1yOGL4zNNTJlkfL+zs5qxRnHlUsn6YAlbW/5HKocfpxVwBw==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [linux]

  '@tauri-apps/cli-linux-x64-gnu@1.6.3':
    resolution: {integrity: sha512-Uvi7M+NK3tAjCZEY1WGel+dFlzJmqcvu3KND+nqa22762NFmOuBIZ4KJR/IQHfpEYqKFNUhJfCGnpUDfiC3Oxg==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [linux]

  '@tauri-apps/cli-linux-x64-musl@1.6.3':
    resolution: {integrity: sha512-rc6B342C0ra8VezB/OJom9j/N+9oW4VRA4qMxS2f4bHY2B/z3J9NPOe6GOILeg4v/CV62ojkLsC3/K/CeF3fqQ==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [linux]

  '@tauri-apps/cli-win32-arm64-msvc@1.6.3':
    resolution: {integrity: sha512-cSH2qOBYuYC4UVIFtrc1YsGfc5tfYrotoHrpTvRjUGu0VywvmyNk82+ZsHEnWZ2UHmu3l3lXIGRqSWveLln0xg==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [win32]

  '@tauri-apps/cli-win32-ia32-msvc@1.6.3':
    resolution: {integrity: sha512-T8V6SJQqE4PSWmYBl0ChQVmS6AR2hXFHURH2DwAhgSGSQ6uBXgwlYFcfIeQpBQA727K2Eq8X2hGfvmoySyHMRw==}
    engines: {node: '>= 10'}
    cpu: [ia32]
    os: [win32]

  '@tauri-apps/cli-win32-x64-msvc@1.6.3':
    resolution: {integrity: sha512-HUkWZ+lYHI/Gjkh2QjHD/OBDpqLVmvjZGpLK9losur1Eg974Jip6k+vsoTUxQBCBDfj30eDBct9E1FvXOspWeg==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [win32]

  '@tauri-apps/cli@1.6.3':
    resolution: {integrity: sha512-q46umd6QLRKDd4Gg6WyZBGa2fWvk0pbeUA5vFomm4uOs1/17LIciHv2iQ4UD+2Yv5H7AO8YiE1t50V0POiEGEw==}
    engines: {node: '>= 10'}
    hasBin: true

  semver@7.7.2:
    resolution: {integrity: sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==}
    engines: {node: '>=10'}
    hasBin: true

snapshots:

  '@tauri-apps/cli-darwin-arm64@1.6.3':
    optional: true

  '@tauri-apps/cli-darwin-x64@1.6.3':
    optional: true

  '@tauri-apps/cli-linux-arm-gnueabihf@1.6.3':
    optional: true

  '@tauri-apps/cli-linux-arm64-gnu@1.6.3':
    optional: true

  '@tauri-apps/cli-linux-arm64-musl@1.6.3':
    optional: true

  '@tauri-apps/cli-linux-x64-gnu@1.6.3':
    optional: true

  '@tauri-apps/cli-linux-x64-musl@1.6.3':
    optional: true

  '@tauri-apps/cli-win32-arm64-msvc@1.6.3':
    optional: true

  '@tauri-apps/cli-win32-ia32-msvc@1.6.3':
    optional: true

  '@tauri-apps/cli-win32-x64-msvc@1.6.3':
    optional: true

  '@tauri-apps/cli@1.6.3':
    dependencies:
      semver: 7.7.2
    optionalDependencies:
      '@tauri-apps/cli-darwin-arm64': 1.6.3
      '@tauri-apps/cli-darwin-x64': 1.6.3
      '@tauri-apps/cli-linux-arm-gnueabihf': 1.6.3
      '@tauri-apps/cli-linux-arm64-gnu': 1.6.3
      '@tauri-apps/cli-linux-arm64-musl': 1.6.3
      '@tauri-apps/cli-linux-x64-gnu': 1.6.3
      '@tauri-apps/cli-linux-x64-musl': 1.6.3
      '@tauri-apps/cli-win32-arm64-msvc': 1.6.3
      '@tauri-apps/cli-win32-ia32-msvc': 1.6.3
      '@tauri-apps/cli-win32-x64-msvc': 1.6.3

  semver@7.7.2: {}

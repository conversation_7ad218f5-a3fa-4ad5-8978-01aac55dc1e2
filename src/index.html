<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="stylesheet" href="styles.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>mohsinDev369 Notifier</title>
    <script type="module" src="/main.js" defer></script>
    <style>
      .logo.vanilla:hover {
        filter: drop-shadow(0 0 2em #ffe21c);
      }
    </style>
  </head>

  <body>
    <div class="container">
      <h1>Welcome to Mohsin Notifier</h1>
      <p>Click on the button  to send notification</p>
      <div class="row">
        <div>
          <input id="toast-input" placeholder="Enter a toast text" />
          <button id="toast-button" type="button">Send</button>
        </div>
      </div>

      <p id="toast-msg"></p>
    </div>
  </body>
</html>

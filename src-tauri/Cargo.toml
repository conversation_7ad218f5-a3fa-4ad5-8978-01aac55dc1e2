[package]
name = "mohsindev369-notification-app"
version = "0.0.0"
description = "A Tauri App to test notification"
authors = ["mohsindev369"]
license = "MIT"
repository = "https://github.com/MohsinDev369/custom-tauri-notifications.git"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
tauri-build = { version = "1.2", features = [] }

[dependencies]
tauri = { version = "1.2", features = ["notification-all", "shell-open"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

[features]
# this feature is used for production builds or when `devPath` points to the filesystem
# DO NOT REMOVE!!
custom-protocol = ["tauri/custom-protocol"]

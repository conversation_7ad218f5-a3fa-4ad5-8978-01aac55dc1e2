{"build": {"beforeDevCommand": "", "beforeBuildCommand": "", "devPath": "../src", "distDir": "../src", "withGlobalTauri": true}, "package": {"productName": "mohsinDev369 Notifier", "version": "0.0.1"}, "tauri": {"allowlist": {"notification": {"all": true}, "all": false, "shell": {"all": false, "open": true}}, "bundle": {"active": true, "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "identifier": "mohsindev369-notification-app", "targets": "all"}, "security": {"csp": null}, "updater": {"active": false}, "windows": [{"fullscreen": false, "resizable": true, "title": "tauri-app", "width": 600, "height": 300}]}}
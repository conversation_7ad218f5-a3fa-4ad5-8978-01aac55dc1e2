{"name": "tauri-app", "version": "0.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "tauri-app", "version": "0.0.0", "devDependencies": {"@tauri-apps/cli": "^1.2.3"}}, "node_modules/@tauri-apps/cli": {"version": "1.2.3", "resolved": "https://registry.npmjs.org/@tauri-apps/cli/-/cli-1.2.3.tgz", "integrity": "sha512-erxtXuPhMEGJPBtnhPILD4AjuT81GZsraqpFvXAmEJZ2p8P6t7MVBifCL8LznRknznM3jn90D3M8RNBP3wcXTw==", "dev": true, "bin": {"tauri": "tauri.js"}, "engines": {"node": ">= 10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/tauri"}, "optionalDependencies": {"@tauri-apps/cli-darwin-arm64": "1.2.3", "@tauri-apps/cli-darwin-x64": "1.2.3", "@tauri-apps/cli-linux-arm-gnueabihf": "1.2.3", "@tauri-apps/cli-linux-arm64-gnu": "1.2.3", "@tauri-apps/cli-linux-arm64-musl": "1.2.3", "@tauri-apps/cli-linux-x64-gnu": "1.2.3", "@tauri-apps/cli-linux-x64-musl": "1.2.3", "@tauri-apps/cli-win32-ia32-msvc": "1.2.3", "@tauri-apps/cli-win32-x64-msvc": "1.2.3"}}, "node_modules/@tauri-apps/cli-darwin-arm64": {"version": "1.2.3", "resolved": "https://registry.npmjs.org/@tauri-apps/cli-darwin-arm64/-/cli-darwin-arm64-1.2.3.tgz", "integrity": "sha512-phJN3fN8FtZZwqXg08bcxfq1+X1JSDglLvRxOxB7VWPq+O5SuB8uLyssjJsu+PIhyZZnIhTGdjhzLSFhSXfLsw==", "cpu": ["arm64"], "dev": true, "optional": true, "os": ["darwin"], "engines": {"node": ">= 10"}}, "node_modules/@tauri-apps/cli-darwin-x64": {"version": "1.2.3", "resolved": "https://registry.npmjs.org/@tauri-apps/cli-darwin-x64/-/cli-darwin-x64-1.2.3.tgz", "integrity": "sha512-jFZ/y6z8z6v4yliIbXKBXA7BJgtZVMsITmEXSuD6s5+eCOpDhQxbRkr6CA+FFfr+/r96rWSDSgDenDQuSvPAKw==", "cpu": ["x64"], "dev": true, "optional": true, "os": ["darwin"], "engines": {"node": ">= 10"}}, "node_modules/@tauri-apps/cli-linux-arm-gnueabihf": {"version": "1.2.3", "resolved": "https://registry.npmjs.org/@tauri-apps/cli-linux-arm-gnueabihf/-/cli-linux-arm-gnueabihf-1.2.3.tgz", "integrity": "sha512-C7h5vqAwXzY0kRGSU00Fj8PudiDWFCiQqqUNI1N+fhCILrzWZB9TPBwdx33ZfXKt/U4+emdIoo/N34v3TiAOmQ==", "cpu": ["arm"], "dev": true, "optional": true, "os": ["linux"], "engines": {"node": ">= 10"}}, "node_modules/@tauri-apps/cli-linux-arm64-gnu": {"version": "1.2.3", "resolved": "https://registry.npmjs.org/@tauri-apps/cli-linux-arm64-gnu/-/cli-linux-arm64-gnu-1.2.3.tgz", "integrity": "sha512-buf1c8sdkuUzVDkGPQpyUdAIIdn5r0UgXU6+H5fGPq/Xzt5K69JzXaeo6fHsZEZghbV0hOK+taKV4J0m30UUMQ==", "cpu": ["arm64"], "dev": true, "optional": true, "os": ["linux"], "engines": {"node": ">= 10"}}, "node_modules/@tauri-apps/cli-linux-arm64-musl": {"version": "1.2.3", "resolved": "https://registry.npmjs.org/@tauri-apps/cli-linux-arm64-musl/-/cli-linux-arm64-musl-1.2.3.tgz", "integrity": "sha512-x88wPS9W5xAyk392vc4uNHcKBBvCp0wf4H9JFMF9OBwB7vfd59LbQCFcPSu8f0BI7bPrOsyHqspWHuFL8ojQEA==", "cpu": ["arm64"], "dev": true, "optional": true, "os": ["linux"], "engines": {"node": ">= 10"}}, "node_modules/@tauri-apps/cli-linux-x64-gnu": {"version": "1.2.3", "resolved": "https://registry.npmjs.org/@tauri-apps/cli-linux-x64-gnu/-/cli-linux-x64-gnu-1.2.3.tgz", "integrity": "sha512-ZMz1jxEVe0B4/7NJnlPHmwmSIuwiD6ViXKs8F+OWWz2Y4jn5TGxWKFg7DLx5OwQTRvEIZxxT7lXHi5CuTNAxKg==", "cpu": ["x64"], "dev": true, "optional": true, "os": ["linux"], "engines": {"node": ">= 10"}}, "node_modules/@tauri-apps/cli-linux-x64-musl": {"version": "1.2.3", "resolved": "https://registry.npmjs.org/@tauri-apps/cli-linux-x64-musl/-/cli-linux-x64-musl-1.2.3.tgz", "integrity": "sha512-B/az59EjJhdbZDzawEVox0LQu2ZHCZlk8rJf85AMIktIUoAZPFbwyiUv7/zjzA/sY6Nb58OSJgaPL2/IBy7E0A==", "cpu": ["x64"], "dev": true, "optional": true, "os": ["linux"], "engines": {"node": ">= 10"}}, "node_modules/@tauri-apps/cli-win32-ia32-msvc": {"version": "1.2.3", "resolved": "https://registry.npmjs.org/@tauri-apps/cli-win32-ia32-msvc/-/cli-win32-ia32-msvc-1.2.3.tgz", "integrity": "sha512-ypdO1OdC5ugNJAKO2m3sb1nsd+0TSvMS9Tr5qN/ZSMvtSduaNwrcZ3D7G/iOIanrqu/Nl8t3LYlgPZGBKlw7Ng==", "cpu": ["ia32"], "dev": true, "optional": true, "os": ["win32"], "engines": {"node": ">= 10"}}, "node_modules/@tauri-apps/cli-win32-x64-msvc": {"version": "1.2.3", "resolved": "https://registry.npmjs.org/@tauri-apps/cli-win32-x64-msvc/-/cli-win32-x64-msvc-1.2.3.tgz", "integrity": "sha512-CsbHQ+XhnV/2csOBBDVfH16cdK00gNyNYUW68isedmqcn8j+s0e9cQ1xXIqi+Hue3awp8g3ImYN5KPepf3UExw==", "cpu": ["x64"], "dev": true, "optional": true, "os": ["win32"], "engines": {"node": ">= 10"}}}}